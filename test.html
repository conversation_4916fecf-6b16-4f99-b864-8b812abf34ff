<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片选择和缩放测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .box_area {
            width: 300px;
            height: 300px;
            border: 2px dashed #ccc;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            background: #fafafa;
        }
        .select_img {
            max-width: 100%;
            max-height: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: auto;
            touch-action: none;
            user-select: none;
            cursor: move;
        }
        .add {
            width: 60px;
            height: 60px;
            background: #007bff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .add:hover {
            background: #0056b3;
        }
        .tip {
            text-align: center;
            color: #666;
            margin-top: 10px;
        }
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .info h3 {
            margin-top: 0;
            color: #333;
        }
        .info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .info li {
            margin: 5px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片选择和缩放测试</h1>
        
        <div class="box_area">
            <img v-if="select_img" :src="select_img" class="select_img"
                 :style="imageStyle"
                 @touchstart="handleTouchStart"
                 @touchmove="handleTouchMove"
                 @touchend="handleTouchEnd"
                 @mousedown="handleMouseDown"
                 @mousemove="handleMouseMove"
                 @mouseup="handleMouseUp"
                 @wheel="handleWheel">
            <div v-if="!select_img" class="add" @click="selectImage">+</div>
            <input type="file" ref="fileInput" @change="handleFileSelect" accept="image/*" style="display: none;">
        </div>
        
        <div class="tip">{{ select_img ? '可以手动调整图片大小哦~' : '点击+号选择图片' }}</div>
        
        <div class="info">
            <h3>使用说明：</h3>
            <ul>
                <li>点击+号选择图片</li>
                <li><strong>移动端：</strong>单指拖动移动，双指缩放</li>
                <li><strong>桌面端：</strong>鼠标拖动移动，滚轮缩放</li>
                <li>缩放范围：0.5倍 - 3倍</li>
            </ul>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                const select_img = ref('');
                const fileInput = ref(null);
                
                // 图片变换相关状态
                const imageTransform = ref({
                    scale: 1,
                    translateX: 0,
                    translateY: 0
                });
                
                // 触摸相关状态
                const touchState = ref({
                    isTouch: false,
                    startDistance: 0,
                    startScale: 1,
                    startX: 0,
                    startY: 0,
                    startTranslateX: 0,
                    startTranslateY: 0,
                    touches: []
                });
                
                // 计算图片样式
                const imageStyle = computed(() => {
                    return {
                        transform: `translate(${imageTransform.value.translateX}px, ${imageTransform.value.translateY}px) scale(${imageTransform.value.scale})`,
                        transformOrigin: 'center center',
                        transition: touchState.value.isTouch ? 'none' : 'transform 0.3s ease'
                    };
                });
                
                // 选择图片
                const selectImage = () => {
                    fileInput.value.click();
                };
                
                // 处理文件选择
                const handleFileSelect = (event) => {
                    const file = event.target.files[0];
                    if (file && file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            select_img.value = e.target.result;
                            // 重置图片变换状态
                            imageTransform.value = {
                                scale: 1,
                                translateX: 0,
                                translateY: 0
                            };
                        };
                        reader.readAsDataURL(file);
                    }
                };
                
                // 计算两点间距离
                const getDistance = (touch1, touch2) => {
                    const dx = touch1.clientX - touch2.clientX;
                    const dy = touch1.clientY - touch2.clientY;
                    return Math.sqrt(dx * dx + dy * dy);
                };
                
                // 触摸开始
                const handleTouchStart = (event) => {
                    event.preventDefault();
                    touchState.value.isTouch = true;
                    touchState.value.touches = Array.from(event.touches);
                    
                    if (event.touches.length === 1) {
                        // 单指拖动
                        touchState.value.startX = event.touches[0].clientX;
                        touchState.value.startY = event.touches[0].clientY;
                        touchState.value.startTranslateX = imageTransform.value.translateX;
                        touchState.value.startTranslateY = imageTransform.value.translateY;
                    } else if (event.touches.length === 2) {
                        // 双指缩放
                        touchState.value.startDistance = getDistance(event.touches[0], event.touches[1]);
                        touchState.value.startScale = imageTransform.value.scale;
                    }
                };
                
                // 触摸移动
                const handleTouchMove = (event) => {
                    event.preventDefault();
                    
                    if (event.touches.length === 1 && touchState.value.touches.length === 1) {
                        // 单指拖动
                        const deltaX = event.touches[0].clientX - touchState.value.startX;
                        const deltaY = event.touches[0].clientY - touchState.value.startY;
                        
                        imageTransform.value.translateX = touchState.value.startTranslateX + deltaX;
                        imageTransform.value.translateY = touchState.value.startTranslateY + deltaY;
                    } else if (event.touches.length === 2 && touchState.value.touches.length === 2) {
                        // 双指缩放
                        const currentDistance = getDistance(event.touches[0], event.touches[1]);
                        const scale = (currentDistance / touchState.value.startDistance) * touchState.value.startScale;
                        
                        // 限制缩放范围
                        imageTransform.value.scale = Math.max(0.5, Math.min(3, scale));
                    }
                };
                
                // 触摸结束
                const handleTouchEnd = (event) => {
                    event.preventDefault();
                    touchState.value.isTouch = false;
                    touchState.value.touches = Array.from(event.touches);
                };

                // 鼠标事件处理（桌面端支持）
                const mouseState = ref({
                    isDragging: false,
                    startX: 0,
                    startY: 0,
                    startTranslateX: 0,
                    startTranslateY: 0
                });

                const handleMouseDown = (event) => {
                    event.preventDefault();
                    mouseState.value.isDragging = true;
                    mouseState.value.startX = event.clientX;
                    mouseState.value.startY = event.clientY;
                    mouseState.value.startTranslateX = imageTransform.value.translateX;
                    mouseState.value.startTranslateY = imageTransform.value.translateY;
                };

                const handleMouseMove = (event) => {
                    if (!mouseState.value.isDragging) return;
                    event.preventDefault();

                    const deltaX = event.clientX - mouseState.value.startX;
                    const deltaY = event.clientY - mouseState.value.startY;

                    imageTransform.value.translateX = mouseState.value.startTranslateX + deltaX;
                    imageTransform.value.translateY = mouseState.value.startTranslateY + deltaY;
                };

                const handleMouseUp = (event) => {
                    event.preventDefault();
                    mouseState.value.isDragging = false;
                };

                const handleWheel = (event) => {
                    event.preventDefault();
                    const delta = event.deltaY > 0 ? -0.1 : 0.1;
                    const newScale = imageTransform.value.scale + delta;
                    imageTransform.value.scale = Math.max(0.5, Math.min(3, newScale));
                };

                return {
                    select_img,
                    fileInput,
                    imageStyle,
                    selectImage,
                    handleFileSelect,
                    handleTouchStart,
                    handleTouchMove,
                    handleTouchEnd,
                    handleMouseDown,
                    handleMouseMove,
                    handleMouseUp,
                    handleWheel
                };
            }
        }).mount('.container');
    </script>
</body>
</html>
