// tabs-component.js
const TabsComponent = {
    props: {
        beforeChange: {
            type: Function,
            default: () => true
        }
    },
    emits: ['tab-change'],
    setup(props, { emit }) {
        // 根据当前URL判断默认激活哪个标签页
        const getDefaultActiveTab = () => {
            const url = window.location.href.toLowerCase();
            if (url.includes('baoming')) return 1;
            if (url.includes('center')) return 2;
            if (url.includes('intr')) return 2;
            return 0; // 默认激活第一个标签页（包含index或其他情况）
        };
        
        const activeTab = Vue.ref(getDefaultActiveTab());
        const pickshow = Vue.ref(false);
        const pickshow2 = Vue.ref(false);
        const noShow = ()=>{
            pickshow.value = false
            pickshow2.value = false
        }
        const handleTabClick = (index) => {
            // 如果当前已经是激活状态，不需要触发切换
            // if (activeTab.value === index) return
            if(index === 1){
                pickshow.value = true;
                pickshow2.value = false;
                return
            }
            if(index === 2){
                pickshow2.value = true;
                pickshow.value = false;
                return
            }
            // 调用切换前的回调，只有返回 true 才允许切换
            const canChange = props.beforeChange ? props.beforeChange(activeTab.value, index) : true
            
            if (canChange) {
                if(index === 0){
                    window.location.replace('index');
                }else if(index === 1){
                    window.location.replace('baoming');
                }else if(index === 2){
                    window.location.replace('center');
                }
                // activeTab.value = index
                // 可以在这里添加切换页面的逻辑
                emit('tab-change', index)
            }
        }
        const goBaoming = () => {
            window.location.href = `baoming`;
        }
        const goCenter = () => {
            window.location.href = `center`;
        }
        const goIntr = () => {
            window.location.href = `intr`;
        }

        

        const offset = Vue.ref({ x: 400, y: 600 });
        //根据窗口宽高将offset设置为窗口右下角
        // 用sessionStorage记录xy坐标，当页面刷新时，读取sessionStorage中的坐标，当坐标不存在的时候，才重新计算坐标
        Vue.watch(offset, (newVal) => {
            sessionStorage.setItem('offset', JSON.stringify(newVal));
        });
        const handleOffset = () => {
            const storedOffset = sessionStorage.getItem('offset');
            if (storedOffset) {
                offset.value = JSON.parse(storedOffset);
                return;
            }
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            offset.value = { x: screenWidth - 60, y: screenHeight - 120 };
        }
        
        setTimeout(() => {
            handleOffset()
        }, 0);
        const showShare = Vue.ref(false);
        const options = [
            { name: '添加微信', icon: 'wechat' },
            { name: '拨打电话', icon: 'phone' },
        ];
        const showCenter = Vue.ref(false);
        const onSelect = (option) => {
            if(option.icon==='phone'){
                window.location.href = 'tel:13856576286';
            }else{
                showCenter.value = true;
            }
            showShare.value = false;
        };

        return {
            activeTab,
            handleTabClick,
            pickshow,
            pickshow2,
            goBaoming,
            goIntr,
            goCenter,
            offset,
            showShare,
            options,
            onSelect,
            showCenter,
            noShow
        }
    },
    template: `
        <div v-if="pickshow||pickshow2" class="tabs_mask" @click="noShow"></div>
        <div class="tabs">
            <div class="tabitem" @click="handleTabClick(0)" :class="{ active: activeTab === 0 }">
                <div class="tabitem_content">首页</div>
            </div>
            <div class="tabitem" @click="handleTabClick(1)" :class="{ active: activeTab === 1 }">
                <div class="tabitem_content">报名</div>
                <div class="tabitem_hide animate__animated" v-if="pickshow">
                    已报名查询
                    <wx-open-launch-weapp  id="launch-btn" appid="wx5c61bb653e995d8a" path="pages/usercenter/dashboard/index" style="height: 100%;width: 100%;display:block;position: absolute;">
                        <component :is="'script'" type="text/wxtag-template">
                            <button class="btn" style="width: 400px;height:800px;background-color: #fff;opacity: 0;"></button>
                        </component>
                    </wx-open-launch-weapp>
                </div>
                <div class="tabitem_hide animate__animated" v-if="pickshow" @click="goBaoming">我要报名</div>
            </div>
            <div class="tabitem" @click="handleTabClick(2)" :class="{ active: activeTab === 2 }">
                <div class="tabitem_content">活动</div>
                <div class="tabitem_hide animate__animated" v-if="pickshow2" @click="goIntr">活动介绍</div>
                <div class="tabitem_hide animate__animated" v-if="pickshow2" @click="goCenter">活动报道</div>
            </div>
        </div>
    `
    // `        <van-floating-bubble v-model:offset="offset" axis="xy" icon="service-o" @click="showShare = true" :gap="0"></van-floating-bubble>
    //     <van-share-sheet
    //         v-model:show="showShare"
    //         title="联系客服"
    //         :options="options"
    //         @select="onSelect"
    //     ></van-share-sheet>
    //     <van-dialog v-model:show="showCenter" title="长按添加客服微信" show-cancel-button>
    //         <img src="img/qr3.png" class="qr1">
    //     </van-dialog>
    //     <transition name="van-fade">
    //         <div class="mask" v-if="pickshow">
    //             <div class="popup popup2">
    //                 <div class="pick_area1 pulsate-bck" @click="goBaoming(1)"></div>
    //                 <div class="pick_area2 pulsate-bck delay" @click="goBaoming(2)"></div>
    //                 <img src="img/close.png" class="close" @click="pickshow=false">
    //             </div>
    //         </div>
    //     </transition>`
}

// 导出组件
export default TabsComponent;



