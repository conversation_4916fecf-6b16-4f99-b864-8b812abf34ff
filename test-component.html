<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片选择组件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .box_area {
            width: 300px;
            height: 300px;
            border: 2px dashed #ccc;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            background: #fafafa;
        }
        .image-selector-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        .select_img {
            max-width: 100%;
            max-height: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: auto;
            touch-action: none;
            user-select: none;
            cursor: move;
        }
        .add {
            width: 60px;
            height: 60px;
            background: #007bff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .add:hover {
            background: #0056b3;
        }
        .tip {
            text-align: center;
            color: #666;
            margin-top: 10px;
        }
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .info h3 {
            margin-top: 0;
            color: #333;
        }
        .info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .info li {
            margin: 5px 0;
            color: #555;
        }
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <h1>图片选择组件测试</h1>
        
        <div class="box_area">
            <image-selector v-model="selectedImage" @image-change="onImageChange"></image-selector>
        </div>
        
        <div class="tip">{{ selectedImage ? '可以手动调整图片大小哦~' : '点击+号选择图片' }}</div>
        
        <div class="controls">
            <button class="btn" @click="clearImage">清除图片</button>
            <button class="btn" @click="getImageData">获取图片数据</button>
        </div>
        
        <div class="info">
            <h3>使用说明：</h3>
            <ul>
                <li>点击+号选择图片</li>
                <li><strong>移动端：</strong>单指拖动移动，双指缩放</li>
                <li><strong>桌面端：</strong>鼠标拖动移动，滚轮缩放</li>
                <li>缩放范围：0.5倍 - 3倍</li>
            </ul>
            
            <h3>组件特性：</h3>
            <ul>
                <li>✅ 支持v-model双向绑定</li>
                <li>✅ 支持image-change事件监听</li>
                <li>✅ 可配置缩放范围</li>
                <li>✅ 移动端和桌面端兼容</li>
            </ul>
        </div>
        
        <div v-if="imageInfo" class="info">
            <h3>当前图片信息：</h3>
            <p>大小：{{ imageInfo.size }}</p>
            <p>类型：{{ imageInfo.type }}</p>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="js/image-selector-component.js"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            components: {
                'image-selector': ImageSelectorComponent
            },
            setup() {
                const selectedImage = ref('');
                const imageInfo = ref(null);
                
                const onImageChange = (imageData) => {
                    console.log('图片已选择:', imageData);
                    // 解析图片信息
                    if (imageData) {
                        const base64Data = imageData.split(',')[1];
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        
                        imageInfo.value = {
                            size: (byteArray.length / 1024).toFixed(2) + ' KB',
                            type: imageData.substring(5, imageData.indexOf(';'))
                        };
                    } else {
                        imageInfo.value = null;
                    }
                };
                
                const clearImage = () => {
                    selectedImage.value = '';
                    imageInfo.value = null;
                };
                
                const getImageData = () => {
                    if (selectedImage.value) {
                        alert('图片数据长度: ' + selectedImage.value.length + ' 字符');
                    } else {
                        alert('请先选择图片');
                    }
                };
                
                return {
                    selectedImage,
                    imageInfo,
                    onImageChange,
                    clearImage,
                    getImageData
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
