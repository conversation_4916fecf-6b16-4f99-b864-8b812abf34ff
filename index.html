<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>旭爱 我的毕业季flag</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <!-- 首页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===1">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <img class="title animate__animated animate__zoomIn" src="img/title.png">
            <img class="main animate__animated animate__zoomIn" src="img/main.png">
            <div class="start pulsate-bck" @click="start"></div>
            <div class="button_container">
                <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="page=3" v-if="startData.prize">
                <img src="img/button3.png" class="button gray animate__animated animate__fadeInRight" @click="show=1" v-if="!startData.prize">
            </div>
        </div>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <img src="img/title2.png" class="title2 animate__animated animate__zoomIn">
            <img src="img/rule.png" class="rule">
            <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
        </div>
        <!-- 上传页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <img src="img/step1.png" class="step animate__animated animate__zoomIn">
            <div class="box_area">
                <img :src="select_img" class="select_img" v-if="select_img"
                     :style="imageStyle"
                     @touchstart="handleTouchStart"
                     @touchmove="handleTouchMove"
                     @touchend="handleTouchEnd"
                     @mousedown="handleMouseDown"
                     @mousemove="handleMouseMove"
                     @mouseup="handleMouseUp"
                     @wheel="handleWheel">
                <img src="img/box.png" class="box">
                <div class="add" @click="selectImage" v-if="!select_img"></div>
                <input type="file" ref="fileInput" @change="handleFileSelect" accept="image/*" style="display: none;">
            </div>
            <div class="tip" :style="{opacity:select_img?1:0}">可以手动调整图片大小哦~</div>
            <div class="button_container">
                <img src="img/button5.png" class="button animate__animated animate__fadeInLeft" @click="selectImage">
                <img src="img/button6.png" class="button animate__animated animate__fadeInRight" @click="createdPoster">
            </div>
        </div>
        <!-- 无奖品弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <div class="close2" @click="show=0"></div>
                </div>
            </div>
        </transition>
    </div>
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js?v=20"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick, computed } = Vue
    </script>
    <script>
        window.startData = {
            rotation:[],
            jihui: '3',
            prize: '{$prize}',
            ad2: '{$ad}',
            ad: '{$ad}'.replaceAll("\n", "<br>"),
            prizeTime: '{$prizeTime}',
            userInfo: {
                name: '{$name}',
                phone: '{$phone}',
                area: '{$area}',
                address: '{$address}',
            },
            nickname: '{$nickname}',
            avatar: '{$headimgurl}',
            wlq:'{$wlq}',
            endtime: '{$endtime}',
            writeFlag: '{$writeFlag}',
            rule: `{$zt_rule|raw}`,
        }

        const app = createApp({
            setup() {
                const { on, bgClick } = useBgMusic('123.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 3
                        opportunity.value--
                        defaultHttp('gamestart', { status: 1 })
                    } else {
                        show.value = 2
                    }
                }
                // 刷新页面功能
                const { reload, savePage } = useReload()
                if (savePage) { page.value = +savePage }
                // 判断是否是初次进入网页,执行预加载
                const { loadStart, progress, progressShow, startFlag } = cheackStartPopup([])
                loadStart()
                if(startFlag){
                    // page.value = 2
                }
                // 检查未领取
                if (startData.wlq === '1') {
                    vant.showConfirmDialog({
                        title: '温馨提示',
                        message: '您已中奖，请尽快完善领奖信息！',
                        confirmButtonText: '去领取',
                        cancelButtonText: '不领取'
                    }).then(() => {
                        show.value = 0
                        page.value = 4
                    })
                }
                const select_img = ref('')
                const fileInput = ref(null)

                // 图片变换相关状态
                const imageTransform = ref({
                    scale: 1,
                    translateX: 0,
                    translateY: 0
                })

                // 触摸相关状态
                const touchState = ref({
                    isTouch: false,
                    startDistance: 0,
                    startScale: 1,
                    startX: 0,
                    startY: 0,
                    startTranslateX: 0,
                    startTranslateY: 0,
                    touches: []
                })

                // 计算图片样式
                const imageStyle = computed(() => {
                    return {
                        transform: `translate(${imageTransform.value.translateX}px, ${imageTransform.value.translateY}px) scale(${imageTransform.value.scale})`,
                        transformOrigin: 'center center',
                        transition: touchState.value.isTouch ? 'none' : 'transform 0.3s ease'
                    }
                })
                // 选择图片
                const selectImage = () => {
                    fileInput.value.click()
                }

                // 处理文件选择
                const handleFileSelect = (event) => {
                    const file = event.target.files[0]
                    if (file && file.type.startsWith('image/')) {
                        const reader = new FileReader()
                        reader.onload = (e) => {
                            select_img.value = e.target.result
                            // 重置图片变换状态
                            imageTransform.value = {
                                scale: 1,
                                translateX: 0,
                                translateY: 0
                            }
                        }
                        reader.readAsDataURL(file)
                    }
                }

                // 计算两点间距离
                const getDistance = (touch1, touch2) => {
                    const dx = touch1.clientX - touch2.clientX
                    const dy = touch1.clientY - touch2.clientY
                    return Math.sqrt(dx * dx + dy * dy)
                }

                // 触摸开始
                const handleTouchStart = (event) => {
                    event.preventDefault()
                    touchState.value.isTouch = true
                    touchState.value.touches = Array.from(event.touches)

                    if (event.touches.length === 1) {
                        // 单指拖动
                        touchState.value.startX = event.touches[0].clientX
                        touchState.value.startY = event.touches[0].clientY
                        touchState.value.startTranslateX = imageTransform.value.translateX
                        touchState.value.startTranslateY = imageTransform.value.translateY
                    } else if (event.touches.length === 2) {
                        // 双指缩放
                        touchState.value.startDistance = getDistance(event.touches[0], event.touches[1])
                        touchState.value.startScale = imageTransform.value.scale
                    }
                }

                const gameEnd = throttle(async (e) => {
                    const res = await defaultHttp('gameEnd', { cg:e?1:0, timer: 40-gameState.value.timer, score:gameState.value.score }, { status: 1 })
                    if (res.status === 1) {
                        show.value = 3
                    } else if(res.status === 2) {
                        show.value = 4
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 触摸移动
                const handleTouchMove = (event) => {
                    event.preventDefault()

                    if (event.touches.length === 1 && touchState.value.touches.length === 1) {
                        // 单指拖动
                        const deltaX = event.touches[0].clientX - touchState.value.startX
                        const deltaY = event.touches[0].clientY - touchState.value.startY

                        imageTransform.value.translateX = touchState.value.startTranslateX + deltaX
                        imageTransform.value.translateY = touchState.value.startTranslateY + deltaY
                    } else if (event.touches.length === 2 && touchState.value.touches.length === 2) {
                        // 双指缩放
                        const currentDistance = getDistance(event.touches[0], event.touches[1])
                        const scale = (currentDistance / touchState.value.startDistance) * touchState.value.startScale

                        // 限制缩放范围
                        imageTransform.value.scale = Math.max(0.5, Math.min(3, scale))
                    }
                }

                // 触摸结束
                const handleTouchEnd = (event) => {
                    event.preventDefault()
                    touchState.value.isTouch = false
                    touchState.value.touches = Array.from(event.touches)
                }

                // 鼠标事件处理（桌面端支持）
                const mouseState = ref({
                    isDragging: false,
                    startX: 0,
                    startY: 0,
                    startTranslateX: 0,
                    startTranslateY: 0
                })

                const handleMouseDown = (event) => {
                    event.preventDefault()
                    mouseState.value.isDragging = true
                    mouseState.value.startX = event.clientX
                    mouseState.value.startY = event.clientY
                    mouseState.value.startTranslateX = imageTransform.value.translateX
                    mouseState.value.startTranslateY = imageTransform.value.translateY
                }

                const handleMouseMove = (event) => {
                    if (!mouseState.value.isDragging) return
                    event.preventDefault()

                    const deltaX = event.clientX - mouseState.value.startX
                    const deltaY = event.clientY - mouseState.value.startY

                    imageTransform.value.translateX = mouseState.value.startTranslateX + deltaX
                    imageTransform.value.translateY = mouseState.value.startTranslateY + deltaY
                }

                const handleMouseUp = (event) => {
                    event.preventDefault()
                    mouseState.value.isDragging = false
                }

                const handleWheel = (event) => {
                    event.preventDefault()
                    const delta = event.deltaY > 0 ? -0.1 : 0.1
                    const newScale = imageTransform.value.scale + delta
                    imageTransform.value.scale = Math.max(0.5, Math.min(3, newScale))
                }

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultHttp('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '水笔一支', ad: '甜润迎新，笔笔生花', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 5
                        res.data.ad = res.data.ad.replaceAll("\\n", "<br>")
                        prizeData.value = res.data
                    } else if (res.status === 2) {
                        show.value = 6 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })
                const poster = ref('')
                const createdPoster = ()=>{
                    var targetDom = document.querySelector('.poster_area');
                    const setup = {
                        useCORS: true, // 使用跨域
                        height: window.innerHeight - 1, //canvas高, 高度减 1 是为了解决底部出现白线问题
                        width: targetDom.scrollWidth -1, //canvas宽
                        dpi: window.devicePixelRatio * 2 //设备像素比
                    };

                    html2canvas(document.querySelector('.poster_area'),setup).then(canvas=>{
                        poster.value = canvas.toDataURL("image/jpg")
                        show.value = 7
                    });
                }
                return {
                    startData, page, show,
                    on, bgClick,
                    start, reload,
                    prizeData, getPrize,
                    createdPoster,
                    poster, select_img,
                    fileInput, imageStyle,
                    selectImage, handleFileSelect,
                    handleTouchStart, handleTouchMove, handleTouchEnd,
                    handleMouseDown, handleMouseMove, handleMouseUp, handleWheel
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
            <!--分享-->
{include file="share"/}
</body>

</html>



